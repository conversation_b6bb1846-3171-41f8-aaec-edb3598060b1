@mixin clearfix {
    &::before,
    &::after {
        content: " ";
        display: table;
    }

    &::after {
        clear: both;
    }
}


@font-face {
    font-family: 'Lato';
    src: local('Lato'), url(./font/lato400.woff) format('woff');
}

@font-face {
    font-family: 'Lato';
    font-style: normal;
    font-weight: 900;
    src: local('Lato Black'), local('Lato-Black'), url(./font/lato400.woff) format('woff');
}


@keyframes msgBounceIn {
    from, to {
        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1);
    }

    from {
        transform: scale(0);
    }

    40% {
        transform: scale(1.03);
    }

    75% {
        transform: scale(0.98);
    }

    to {
        transform: scale(1);
    }
}

@keyframes dotZoomIn {
    from, 40%, 80%, 100% {
        animation-timing-function: ease-in-out;
    }

    from {
        transform: scale(0);
    }

    40% {
        transform: scale(1);
    }

    80% {
        transform: scale(0);
    }

    100% {
        transform: scale(0);
    }
}


html, body {
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
}

body {
    position: relative;

    font-family: Lato;
    font-size: $font-size-basic;

    background-color: lighten($clr-primary, 50%);
    background-image: radial-gradient(ellipse farthest-corner at 50% 0,
        #DBF5F1 0%, #9BE4D8 100%);
}

ul {
    margin: 0;
    padding: 0;
}

li {
    list-style: none;
}

img {
    max-width: 100%;
}


.primary-bg {
    background: linear-gradient(20deg, #3FD1E1 0%, #44D7CD 100%);
}
